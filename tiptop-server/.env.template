# TipTop Environment Configuration Template
# Copy this file to .env and fill in your actual values

# ===========================================
# REQUIRED - AI/LLM Configuration
# ===========================================
# Grok API (for AI features) - REQUIRED
GROK_API_URL=https://api.groq.com/openai/v1/chat/completions
GROK_API_KEY=your_grok_api_key_here
GROK_MODEL=grok-3-mini-fast-beta

# ===========================================
# REQUIRED - Database Configuration  
# ===========================================
DB_HOST=localhost
DB_USER=postgres
DB_PASSWORD=your_postgres_password
DB_NAME=tiptop
DB_PORT=5432

# ===========================================
# OPTIONAL - Payment Features (Stripe)
# ===========================================
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# ===========================================
# OPTIONAL - Email Configuration
# ===========================================
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=465
EMAIL_FROM=TipTop <<EMAIL>>

# ===========================================
# OPTIONAL - Server Configuration
# ===========================================
PORT=8080
TIMEOUT=15000
SERVER_URL=http://localhost:8080

# ===========================================
# OPTIONAL - Feature Flags
# ===========================================
TIPTOP_TEST_MODE=true
ENABLE_CACHE=true
CACHE_TTL=3600
ENABLE_ASYNC_LOADING=true
ENABLE_SOCIAL_FEATURES=true

# ===========================================
# OPTIONAL - Social Features
# ===========================================
MAX_MESSAGES_PER_PAGE=100
MESSAGE_HISTORY_HOURS=24
MAX_USERS_PER_PAGE=50
HEARTBEAT_INTERVAL=30000
DEFAULT_OPT_OUT=false

# ===========================================
# OPTIONAL - Retry Configuration
# ===========================================
MAX_RETRIES=3
