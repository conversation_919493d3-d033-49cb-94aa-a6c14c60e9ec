# 🚀 TipTop Development Environment Setup

## Quick Start

1. **Run the setup script:**
   ```bash
   cd /Users/<USER>/AI/Tiptop/tiptop/tiptop-server
   ./setup-dev-environment.sh
   ```

2. **Edit environment variables:**
   ```bash
   nano .env  # or use your preferred editor
   ```

3. **Start the services:**
   ```bash
   # Terminal 1 - Cloud Function Server
   cd cloud-function-tiptop
   npm start

   # Terminal 2 - WebSocket Server  
   cd websocket-server
   npm start
   ```

## Required API Keys & Services

### 🤖 **CRITICAL: Grok API (for AI features)**
- **What:** Powers the new content categorization and AI summarization
- **Get it:** Sign up at https://console.groq.com/
- **Set in .env:** `GROK_API_KEY=your_key_here`
- **Cost:** Free tier available

### 🗄️ **Database (PostgreSQL)**
- **What:** Stores user data, social features, caching
- **Setup:** Handled by setup script
- **Set in .env:** `DB_PASSWORD=your_password`

### 💳 **OPTIONAL: Stripe (for payments)**
- **What:** Handles premium subscriptions
- **Get it:** https://dashboard.stripe.com/
- **Set in .env:** `STRIPE_SECRET_KEY=sk_test_...`

### 📧 **OPTIONAL: Email (for notifications)**
- **What:** Sends user notifications
- **Setup:** Use Gmail app password
- **Set in .env:** `EMAIL_USER=` and `EMAIL_PASS=`

## Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Browser Ext    │◄──►│  Cloud Function  │◄──►│   Grok AI API   │
│  (Frontend)     │    │   (Port 8080)    │    │ (Categorization)│
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │
         │              ┌──────────────────┐    ┌─────────────────┐
         └──────────────►│ WebSocket Server │◄──►│   PostgreSQL    │
                         │   (Port 8080)    │    │   (Port 5432)   │
                         └──────────────────┘    └─────────────────┘
```

## New Content Categorization Features

The system now automatically categorizes web content into:

- **📚 Academic/Research:** Uses analogies for non-academic audiences
- **📰 Media/News:** Highlights background stories and context  
- **🔧 Technical:** Provides practical examples and breakdowns
- **🛒 Commercial:** Extracts unbiased insights and comparisons
- **🎓 Educational:** Creates learning paths and connections
- **🎬 Entertainment:** Provides cultural context and insights

## Testing the Setup

1. **Test content categorization:**
   ```bash
   cd cloud-function-tiptop
   node test-categorization.js
   ```

2. **Test database connection:**
   ```bash
   cd websocket-server
   node test-db.js
   ```

3. **Test the full system:**
   - Load extension in Chrome
   - Visit any webpage
   - Click TipTop extension icon
   - Should see categorized content analysis

## Troubleshooting

### Database Issues
```bash
# Restart PostgreSQL
brew services restart postgresql@14

# Check if database exists
psql -l | grep tiptop

# Recreate database
dropdb tiptop && createdb tiptop
```

### API Issues
```bash
# Test Grok API key
curl -H "Authorization: Bearer $GROK_API_KEY" \
     https://api.groq.com/openai/v1/models
```

### Port Conflicts
```bash
# Check what's using port 8080
lsof -i :8080

# Kill process if needed
kill -9 <PID>
```

## Development Workflow

1. **Make changes** to server code
2. **Restart services** (they don't auto-reload)
3. **Test with extension** on various websites
4. **Check logs** in terminal for debugging

## File Structure
```
tiptop-server/
├── cloud-function-tiptop/     # Main API server
│   ├── index.js              # Main server with categorization
│   ├── config.js             # Configuration
│   └── test-categorization.js # Test categorization
├── websocket-server/         # Real-time features
│   ├── server.js            # WebSocket server
│   └── init-db.sql          # Database schema
└── .env                     # Your configuration
```

## Next Steps

1. **Get Grok API key** (most important)
2. **Run setup script**
3. **Test content categorization** on different websites
4. **Explore the new categorization features** in the guide
