#!/bin/bash

# TipTop Development Environment Setup Script
# This script sets up your local development environment

set -e  # Exit on any error

echo "🚀 Setting up TipTop Development Environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_warning ".env file not found. Creating from template..."
    cp .env.template .env
    print_warning "Please edit .env file with your actual configuration values!"
    print_warning "At minimum, you need to set:"
    echo "  - GROK_API_KEY (for AI features)"
    echo "  - DB_PASSWORD (for database)"
    echo ""
    read -p "Press Enter to continue after editing .env file..."
fi

# Start PostgreSQL
print_status "Starting PostgreSQL..."
brew services start postgresql@14 || {
    print_error "Failed to start PostgreSQL. Please install it first:"
    echo "  brew install postgresql@14"
    exit 1
}

# Wait for PostgreSQL to start
sleep 3

# Create database and user
print_status "Setting up database..."
DB_PASSWORD=$(grep "^DB_PASSWORD=" .env | cut -d'=' -f2)
if [ -z "$DB_PASSWORD" ]; then
    print_error "DB_PASSWORD not set in .env file"
    exit 1
fi

# Create database if it doesn't exist
createdb tiptop 2>/dev/null || print_warning "Database 'tiptop' already exists"

# Set password for postgres user
psql -d postgres -c "ALTER USER postgres PASSWORD '$DB_PASSWORD';" 2>/dev/null || {
    print_warning "Could not set postgres password. You may need to do this manually."
}

# Initialize database schema
if [ -f "websocket-server/init-db.sql" ]; then
    print_status "Initializing database schema..."
    psql -d tiptop -f websocket-server/init-db.sql || print_warning "Database schema may already exist"
fi

# Test database connection
print_status "Testing database connection..."
PGPASSWORD=$DB_PASSWORD psql -h localhost -U postgres -d tiptop -c "SELECT 1;" > /dev/null && {
    print_success "Database connection successful!"
} || {
    print_error "Database connection failed. Please check your configuration."
    exit 1
}

# Test content categorization
print_status "Testing content categorization system..."
cd cloud-function-tiptop
node test-categorization.js && {
    print_success "Content categorization test passed!"
} || {
    print_warning "Content categorization test failed, but this is not critical for basic functionality."
}
cd ..

print_success "Development environment setup complete!"
print_status "Next steps:"
echo "  1. Edit .env file with your API keys"
echo "  2. Start the cloud function server: cd cloud-function-tiptop && npm start"
echo "  3. Start the websocket server: cd websocket-server && npm start"
echo "  4. Load the browser extension from tiptop-extension/ directory"
echo ""
print_status "For content categorization features, make sure to set GROK_API_KEY in .env"
