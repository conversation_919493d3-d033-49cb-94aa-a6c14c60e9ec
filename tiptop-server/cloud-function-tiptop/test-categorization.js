/**
 * Test script for content categorization system
 * This script demonstrates how different types of content are categorized
 */

// Import URL for Node.js
const { URL } = require('url');

// Mock the categorization function for testing
function calculateCategoryScore(domain, text, domains, keywords) {
  let score = 0;
  
  // Domain matching (higher weight)
  if (domains.some(d => domain.includes(d))) {
    score += 10;
  }
  
  // Keyword matching
  keywords.forEach(keyword => {
    const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
    const matches = text.match(regex);
    if (matches) {
      score += matches.length * 0.5;
    }
  });
  
  return score;
}

async function categorizeContent(text, url, title) {
  console.log(`Categorizing content for URL: ${url}`);

  // Domain-based categorization
  let domain;
  try {
    domain = new URL(url).hostname.toLowerCase();
  } catch (error) {
    console.error(`Error parsing URL: ${url}`, error.message);
    domain = '';
  }
  
  // Academic/Research indicators
  const academicDomains = ['arxiv.org', 'pubmed.ncbi.nlm.nih.gov', 'scholar.google.com', 'researchgate.net', 'academia.edu', 'jstor.org', 'springer.com', 'nature.com', 'science.org', 'ieee.org'];
  const academicKeywords = ['abstract', 'methodology', 'results', 'conclusion', 'references', 'doi:', 'peer review', 'journal', 'research', 'study', 'analysis', 'hypothesis'];
  
  // Media/News indicators
  const mediaDomains = ['cnn.com', 'bbc.com', 'reuters.com', 'nytimes.com', 'washingtonpost.com', 'theguardian.com', 'medium.com', 'substack.com', 'techcrunch.com', 'wired.com'];
  const mediaKeywords = ['breaking', 'reported', 'sources say', 'according to', 'exclusive', 'investigation', 'interview', 'statement', 'press release'];
  
  // Technical documentation indicators
  const techDomains = ['github.com', 'stackoverflow.com', 'docs.', 'api.', 'developer.', 'documentation'];
  const techKeywords = ['api', 'function', 'method', 'parameter', 'example', 'code', 'syntax', 'tutorial', 'guide', 'installation'];
  
  // Commercial indicators
  const commercialDomains = ['amazon.com', 'ebay.com', 'shopify.com', 'etsy.com'];
  const commercialKeywords = ['price', 'buy', 'purchase', 'product', 'review', 'rating', 'discount', 'sale', 'shipping', 'warranty'];
  
  // Educational indicators
  const educationalDomains = ['coursera.org', 'edx.org', 'khanacademy.org', 'udemy.com', 'mit.edu', 'harvard.edu', 'stanford.edu'];
  const educationalKeywords = ['course', 'lesson', 'chapter', 'learning', 'student', 'teacher', 'assignment', 'quiz', 'exam'];
  
  // Entertainment indicators
  const entertainmentDomains = ['imdb.com', 'rottentomatoes.com', 'metacritic.com', 'entertainment.com', 'variety.com'];
  const entertainmentKeywords = ['movie', 'film', 'actor', 'director', 'review', 'rating', 'box office', 'premiere', 'trailer'];

  const textLower = (text + ' ' + title).toLowerCase();
  
  // Calculate scores for each category
  const scores = {
    academic: calculateCategoryScore(domain, textLower, academicDomains, academicKeywords),
    media: calculateCategoryScore(domain, textLower, mediaDomains, mediaKeywords),
    technical: calculateCategoryScore(domain, textLower, techDomains, techKeywords),
    commercial: calculateCategoryScore(domain, textLower, commercialDomains, commercialKeywords),
    educational: calculateCategoryScore(domain, textLower, educationalDomains, educationalKeywords),
    entertainment: calculateCategoryScore(domain, textLower, entertainmentDomains, entertainmentKeywords)
  };
  
  // Find the category with the highest score
  const maxScore = Math.max(...Object.values(scores));
  const category = Object.keys(scores).find(key => scores[key] === maxScore);
  
  console.log(`Content categorized as: ${category} (score: ${maxScore})`);
  return { category, scores, confidence: maxScore };
}

// Test cases
const testCases = [
  {
    url: "https://arxiv.org/abs/2301.12345",
    title: "Deep Learning Approaches for Natural Language Processing",
    text: "Abstract: This paper presents a novel methodology for natural language processing using deep learning. Our research shows significant improvements in accuracy. The study includes comprehensive analysis of results and references to peer-reviewed journals."
  },
  {
    url: "https://techcrunch.com/2024/01/15/startup-funding",
    title: "Breaking: Tech Startup Raises $50M in Series B",
    text: "According to sources close to the deal, the startup has raised significant funding. The exclusive investigation reveals that investors are bullish on the company's prospects. The press release will be issued tomorrow."
  },
  {
    url: "https://docs.github.com/api/rest",
    title: "GitHub REST API Documentation",
    text: "This guide explains how to use the GitHub API. The function takes several parameters and returns JSON data. Here's an example of the syntax for making API calls. Follow this tutorial for installation and setup."
  },
  {
    url: "https://amazon.com/product/wireless-headphones",
    title: "Premium Wireless Headphones - Customer Reviews",
    text: "This product has excellent reviews and ratings. The price is competitive and includes free shipping. Customers love the sound quality and battery life. The warranty covers manufacturing defects for two years."
  },
  {
    url: "https://coursera.org/learn/machine-learning",
    title: "Machine Learning Course by Stanford University",
    text: "This course covers fundamental concepts of machine learning. Students will complete assignments and quizzes throughout the program. The curriculum includes practical lessons and hands-on learning experiences."
  },
  {
    url: "https://imdb.com/title/tt1234567",
    title: "The Latest Blockbuster Movie Review",
    text: "This film features outstanding performances by the lead actors. The director's vision is clearly evident throughout. Critics have given it high ratings and it's performing well at the box office. The premiere was attended by major celebrities."
  }
];

// Run tests
async function runTests() {
  console.log("=== Content Categorization Test Results ===\n");
  
  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    console.log(`Test ${i + 1}: ${testCase.title}`);
    console.log(`URL: ${testCase.url}`);
    
    const result = await categorizeContent(testCase.text, testCase.url, testCase.title);
    
    console.log(`Category: ${result.category}`);
    console.log(`Confidence: ${result.confidence}`);
    console.log(`All Scores:`, result.scores);
    console.log("---\n");
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { categorizeContent, runTests };
} else {
  // Run tests if this file is executed directly
  runTests().catch(console.error);
}
