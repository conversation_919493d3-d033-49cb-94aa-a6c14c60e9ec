{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../src/util.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;GAgBG;;;AAEH,yBAAyB;AACzB,6BAA6B;AAC7B,uCAAuC;AAEvC,SAAS,sBAAsB,CAAC,IAAmB,EAAE,YAAsB;IACzE,MAAM,mBAAmB,GAAG,IAAI,CAAC,WAAW,CAAC;IAC7C,IAAI,CAAC,WAAW,GAAG,CAAC,MAAc,EAAE,MAAc,EAAE,EAAE;QACpD,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;YAC3B,OAAO,MAAM,CAAC;SACf;QACD,KAAK,MAAM,SAAS,IAAI,YAAY,EAAE;YACpC,MAAM,QAAQ,GAAW,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YACtD,IAAI;gBACF,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAC3C,OAAO,QAAQ,CAAC;aACjB;YAAC,OAAO,GAAG,EAAE;gBACZ,SAAS;aACV;SACF;QACD,OAAO,CAAC,WAAW,CAAC,GAAG,MAAM,0CAA0C,YAAY,EAAE,CAAC,CAAC;QACvF,OAAO,mBAAmB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC7C,CAAC,CAAC;AACJ,CAAC;AAOM,KAAK,UAAU,qBAAqB,CACzC,QAA2B,EAC3B,OAAiB;IAEjB,MAAM,IAAI,GAAkB,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;IAChD,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;IACxB,IAAI,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE;QACzB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;YACvC,OAAO,OAAO,CAAC,MAAM,CACnB,IAAI,KAAK,CAAC,yCAAyC,CAAC,CACrD,CAAC;SACH;QACD,sBAAsB,CAAC,IAAI,EAAE,OAAO,CAAC,WAAuB,CAAC,CAAC;KAC/D;IACD,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IACtD,UAAU,CAAC,UAAU,EAAE,CAAC;IACxB,OAAO,UAAU,CAAC;AACpB,CAAC;AAjBD,sDAiBC;AAED,SAAgB,yBAAyB,CACvC,QAA2B,EAC3B,OAAiB;IAEjB,MAAM,IAAI,GAAkB,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;IAChD,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;IACxB,IAAI,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE;QACzB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;YACvC,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;SAC5D;QACD,sBAAsB,CAAC,IAAI,EAAE,OAAO,CAAC,WAAuB,CAAC,CAAC;KAC/D;IACD,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IACpD,UAAU,CAAC,UAAU,EAAE,CAAC;IACxB,OAAO,UAAU,CAAC;AACpB,CAAC;AAfD,8DAeC;AAED;;GAEG;AACH,SAAgB,eAAe;IAC7B,4EAA4E;IAC5E,qEAAqE;IAErE,mEAAmE;IACnE,MAAM,aAAa,GAAG,OAAO,CAAC,qCAAqC,CAAC,CAAC;IACrE,MAAM,oBAAoB,GAAG,OAAO,CAAC,4CAA4C,CAAC,CAAC;IACnF,MAAM,uBAAuB,GAAG,OAAO,CAAC,gDAAgD,CAAC,CAAC;IAC1F,MAAM,cAAc,GAAG,OAAO,CAAC,sCAAsC,CAAC,CAAC;IAEvE,QAAQ,CAAC,MAAM,CACb,KAAK,EACL,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CACnD,CAAC;IACF,QAAQ,CAAC,MAAM,CACb,YAAY,EACZ,oBAAoB,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAC1D,CAAC;IACF,QAAQ,CAAC,MAAM,CACb,gBAAgB,EAChB,uBAAuB,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAC7D,CAAC;IACF,QAAQ,CAAC,MAAM,CACb,MAAM,EACN,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CACpD,CAAC;AACJ,CAAC;AA1BD,0CA0BC"}