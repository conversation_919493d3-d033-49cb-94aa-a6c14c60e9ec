{"version": 3, "file": "parser.js", "sourceRoot": "", "sources": ["../../src/_vendor/partial-json-parser/parser.ts"], "names": [], "mappings": ";;;AAAA,MAAM,GAAG,GAAG,WAAW,CAAC;AACxB,MAAM,GAAG,GAAG,WAAW,CAAC;AACxB,MAAM,GAAG,GAAG,WAAW,CAAC;AACxB,MAAM,GAAG,GAAG,WAAW,CAAC;AACxB,MAAM,IAAI,GAAG,WAAW,CAAC;AACzB,MAAM,IAAI,GAAG,WAAW,CAAC;AACzB,MAAM,GAAG,GAAG,WAAW,CAAC;AACxB,MAAM,QAAQ,GAAG,WAAW,CAAC;AAC7B,MAAM,cAAc,GAAG,WAAW,CAAC;AAEnC,MAAM,GAAG,GAAG,QAAQ,GAAG,cAAc,CAAC;AACtC,MAAM,OAAO,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC;AACxC,MAAM,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,OAAO,CAAC;AACjC,MAAM,UAAU,GAAG,GAAG,GAAG,GAAG,CAAC;AAC7B,MAAM,GAAG,GAAG,IAAI,GAAG,UAAU,CAAC;AAE9B,MAAM,KAAK,GAAG;IACZ,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,GAAG;IACH,QAAQ;IACR,cAAc;IACd,GAAG;IACH,OAAO;IACP,IAAI;IACJ,UAAU;IACV,GAAG;CACJ,CAAC;AAEF,6DAA6D;AAC7D,MAAM,WAAY,SAAQ,KAAK;CAAG;AAoNX,kCAAW;AAlNlC,MAAM,aAAc,SAAQ,KAAK;CAAG;AAkNA,sCAAa;AAhNjD;;;;;;;GAOG;AACH,SAAS,SAAS,CAAC,UAAkB,EAAE,eAAuB,KAAK,CAAC,GAAG;IACrE,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;QAClC,MAAM,IAAI,SAAS,CAAC,sBAAsB,OAAO,UAAU,EAAE,CAAC,CAAC;KAChE;IACD,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE;QACtB,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,WAAW,CAAC,CAAC;KAC3C;IACD,OAAO,UAAU,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,CAAC;AACrD,CAAC;AAED,MAAM,UAAU,GAAG,CAAC,UAAkB,EAAE,KAAa,EAAE,EAAE;IACvD,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;IACjC,IAAI,KAAK,GAAG,CAAC,CAAC;IAEd,MAAM,eAAe,GAAG,CAAC,GAAW,EAAE,EAAE;QACtC,MAAM,IAAI,WAAW,CAAC,GAAG,GAAG,gBAAgB,KAAK,EAAE,CAAC,CAAC;IACvD,CAAC,CAAC;IAEF,MAAM,mBAAmB,GAAG,CAAC,GAAW,EAAE,EAAE;QAC1C,MAAM,IAAI,aAAa,CAAC,GAAG,GAAG,gBAAgB,KAAK,EAAE,CAAC,CAAC;IACzD,CAAC,CAAC;IAEF,MAAM,QAAQ,GAAc,GAAG,EAAE;QAC/B,SAAS,EAAE,CAAC;QACZ,IAAI,KAAK,IAAI,MAAM;YAAE,eAAe,CAAC,yBAAyB,CAAC,CAAC;QAChE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,GAAG;YAAE,OAAO,QAAQ,EAAE,CAAC;QACjD,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,GAAG;YAAE,OAAO,QAAQ,EAAE,CAAC;QACjD,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,GAAG;YAAE,OAAO,QAAQ,EAAE,CAAC;QACjD,IACE,UAAU,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,KAAK,MAAM;YACjD,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,IAAI,MAAM,GAAG,KAAK,GAAG,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAC5F;YACA,KAAK,IAAI,CAAC,CAAC;YACX,OAAO,IAAI,CAAC;SACb;QACD,IACE,UAAU,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,KAAK,MAAM;YACjD,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,IAAI,MAAM,GAAG,KAAK,GAAG,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAC5F;YACA,KAAK,IAAI,CAAC,CAAC;YACX,OAAO,IAAI,CAAC;SACb;QACD,IACE,UAAU,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,KAAK,OAAO;YAClD,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,IAAI,MAAM,GAAG,KAAK,GAAG,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAC7F;YACA,KAAK,IAAI,CAAC,CAAC;YACX,OAAO,KAAK,CAAC;SACd;QACD,IACE,UAAU,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,KAAK,UAAU;YACrD,CAAC,KAAK,CAAC,QAAQ,GAAG,KAAK,IAAI,MAAM,GAAG,KAAK,GAAG,CAAC,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EACpG;YACA,KAAK,IAAI,CAAC,CAAC;YACX,OAAO,QAAQ,CAAC;SACjB;QACD,IACE,UAAU,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,KAAK,WAAW;YACtD,CAAC,KAAK,CAAC,cAAc,GAAG,KAAK;gBAC3B,CAAC,GAAG,MAAM,GAAG,KAAK;gBAClB,MAAM,GAAG,KAAK,GAAG,CAAC;gBAClB,WAAW,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EACtD;YACA,KAAK,IAAI,CAAC,CAAC;YACX,OAAO,CAAC,QAAQ,CAAC;SAClB;QACD,IACE,UAAU,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,KAAK,KAAK;YAChD,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK,IAAI,MAAM,GAAG,KAAK,GAAG,CAAC,IAAI,KAAK,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAC1F;YACA,KAAK,IAAI,CAAC,CAAC;YACX,OAAO,GAAG,CAAC;SACZ;QACD,OAAO,QAAQ,EAAE,CAAC;IACpB,CAAC,CAAC;IAEF,MAAM,QAAQ,GAAiB,GAAG,EAAE;QAClC,MAAM,KAAK,GAAG,KAAK,CAAC;QACpB,IAAI,MAAM,GAAG,KAAK,CAAC;QACnB,KAAK,EAAE,CAAC,CAAC,qBAAqB;QAC9B,OAAO,KAAK,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,IAAI,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,EAAE;YAClG,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC;YACtD,KAAK,EAAE,CAAC;SACT;QACD,IAAI,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,GAAG,EAAE;YACnC,IAAI;gBACF,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,EAAE,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;aAC1E;YAAC,OAAO,CAAC,EAAE;gBACV,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;aAChC;SACF;aAAM,IAAI,KAAK,CAAC,GAAG,GAAG,KAAK,EAAE;YAC5B,IAAI;gBACF,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;aAC9E;YAAC,OAAO,CAAC,EAAE;gBACV,uCAAuC;gBACvC,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,EAAE,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;aACpF;SACF;QACD,eAAe,CAAC,6BAA6B,CAAC,CAAC;IACjD,CAAC,CAAC;IAEF,MAAM,QAAQ,GAAG,GAAG,EAAE;QACpB,KAAK,EAAE,CAAC,CAAC,qBAAqB;QAC9B,SAAS,EAAE,CAAC;QACZ,MAAM,GAAG,GAAwB,EAAE,CAAC;QACpC,IAAI;YACF,OAAO,UAAU,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE;gBAChC,SAAS,EAAE,CAAC;gBACZ,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,CAAC,GAAG,GAAG,KAAK;oBAAE,OAAO,GAAG,CAAC;gBACrD,MAAM,GAAG,GAAG,QAAQ,EAAE,CAAC;gBACvB,SAAS,EAAE,CAAC;gBACZ,KAAK,EAAE,CAAC,CAAC,aAAa;gBACtB,IAAI;oBACF,MAAM,KAAK,GAAG,QAAQ,EAAE,CAAC;oBACzB,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;iBAClG;gBAAC,OAAO,CAAC,EAAE;oBACV,IAAI,KAAK,CAAC,GAAG,GAAG,KAAK;wBAAE,OAAO,GAAG,CAAC;;wBAC7B,MAAM,CAAC,CAAC;iBACd;gBACD,SAAS,EAAE,CAAC;gBACZ,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,GAAG;oBAAE,KAAK,EAAE,CAAC,CAAC,aAAa;aACtD;SACF;QAAC,OAAO,CAAC,EAAE;YACV,IAAI,KAAK,CAAC,GAAG,GAAG,KAAK;gBAAE,OAAO,GAAG,CAAC;;gBAC7B,eAAe,CAAC,+BAA+B,CAAC,CAAC;SACvD;QACD,KAAK,EAAE,CAAC,CAAC,mBAAmB;QAC5B,OAAO,GAAG,CAAC;IACb,CAAC,CAAC;IAEF,MAAM,QAAQ,GAAG,GAAG,EAAE;QACpB,KAAK,EAAE,CAAC,CAAC,uBAAuB;QAChC,MAAM,GAAG,GAAG,EAAE,CAAC;QACf,IAAI;YACF,OAAO,UAAU,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE;gBAChC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACrB,SAAS,EAAE,CAAC;gBACZ,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE;oBAC7B,KAAK,EAAE,CAAC,CAAC,aAAa;iBACvB;aACF;SACF;QAAC,OAAO,CAAC,EAAE;YACV,IAAI,KAAK,CAAC,GAAG,GAAG,KAAK,EAAE;gBACrB,OAAO,GAAG,CAAC;aACZ;YACD,eAAe,CAAC,8BAA8B,CAAC,CAAC;SACjD;QACD,KAAK,EAAE,CAAC,CAAC,qBAAqB;QAC9B,OAAO,GAAG,CAAC;IACb,CAAC,CAAC;IAEF,MAAM,QAAQ,GAAG,GAAG,EAAE;QACpB,IAAI,KAAK,KAAK,CAAC,EAAE;YACf,IAAI,UAAU,KAAK,GAAG,IAAI,KAAK,CAAC,GAAG,GAAG,KAAK;gBAAE,eAAe,CAAC,sBAAsB,CAAC,CAAC;YACrF,IAAI;gBACF,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;aAC/B;YAAC,OAAO,CAAC,EAAE;gBACV,IAAI,KAAK,CAAC,GAAG,GAAG,KAAK,EAAE;oBACrB,IAAI;wBACF,IAAI,GAAG,KAAK,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;4BAC3C,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;wBAC1E,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;qBACzE;oBAAC,OAAO,CAAC,EAAE,GAAE;iBACf;gBACD,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;aAChC;SACF;QAED,MAAM,KAAK,GAAG,KAAK,CAAC;QAEpB,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,GAAG;YAAE,KAAK,EAAE,CAAC;QACvC,OAAO,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAE,CAAC;YAAE,KAAK,EAAE,CAAC;QAEzE,IAAI,KAAK,IAAI,MAAM,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC;YAAE,eAAe,CAAC,6BAA6B,CAAC,CAAC;QAE5F,IAAI;YACF,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;SACvD;QAAC,OAAO,CAAC,EAAE;YACV,IAAI,UAAU,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,GAAG,GAAG,KAAK;gBACjE,eAAe,CAAC,sBAAsB,CAAC,CAAC;YAC1C,IAAI;gBACF,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,EAAE,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;aAC7E;YAAC,OAAO,CAAC,EAAE;gBACV,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;aAChC;SACF;IACH,CAAC,CAAC;IAEF,MAAM,SAAS,GAAG,GAAG,EAAE;QACrB,OAAO,KAAK,GAAG,MAAM,IAAI,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAE,CAAC,EAAE;YAC/D,KAAK,EAAE,CAAC;SACT;IACH,CAAC,CAAC;IAEF,OAAO,QAAQ,EAAE,CAAC;AACpB,CAAC,CAAC;AAEF,gEAAgE;AAChE,MAAM,YAAY,GAAG,CAAC,KAAa,EAAE,EAAE,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;AAEvE,oCAAY"}